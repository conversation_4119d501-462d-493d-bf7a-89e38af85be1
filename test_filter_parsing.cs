using System;
using System.Collections.Generic;
using uBuyFirst;

namespace FilterParsingTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Testing filter expression parsing...");
            
            // Test cases
            var testCases = new[]
            {
                "22A-D4P0N104",  // Invalid - should return false
                "'22A-D4P0N104'", // Valid literal - should return true
                "ItemId = '22A-D4P0N104'", // Valid expression - should return true
                "Price > 100", // Valid expression - should return true
                "", // Empty - should return false
                "Invalid{Syntax", // Invalid - should return false
            };
            
            foreach (var testCase in testCases)
            {
                bool isValid = XFilterClass.IsExpressionValid(testCase);
                Console.WriteLine($"Expression: '{testCase}' -> Valid: {isValid}");
            }
            
            // Test Import method with invalid expression
            Console.WriteLine("\nTesting Import method with invalid expression...");
            var filter = new XFilterClass();
            var cells = new List<string>
            {
                "TestFilter", // Alias
                "true", // Enabled
                "OpenBrowser", // Action
                "ItemId", // FormatColumn
                "22A-D4P0N104", // Invalid expression
                "", // SerializedAppearance
                "" // ActionData
            };
            
            string importResult = filter.Import(cells);
            Console.WriteLine($"Import result: {importResult}");
            Console.WriteLine($"Filter enabled: {filter.Enabled}");
            
            Console.WriteLine("Test completed.");
        }
    }
}
